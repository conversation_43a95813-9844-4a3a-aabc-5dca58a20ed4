import EditorComponent from '@/components/Editor/Editor'
import { Dropzone } from '@/components/Form/Dropzone'
import { Icons } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from '@/components/ui/use-toast'
import { bytesToSize } from '@/lib/utils'
import FileService from '@/network/services/file'
import SettingService from '@/network/services/setting'
import Settings, { CreateFreebies, Freebies } from '@/types/Setting'
import { PageBanner } from '@/types/Cms'
import { Image } from '@unpic/react'
import { cx } from 'class-variance-authority'
import { MoreHorizontal, Trash2Icon, Monitor, Smartphone, GripHorizontalIcon } from 'lucide-react'
import { FC, useEffect, useState } from 'react'
import { FileWithPath } from 'react-dropzone'
import { useFieldArray, useForm } from 'react-hook-form'
import { mutate } from 'swr'
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  UniqueIdentifier
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy
} from '@dnd-kit/sortable'
import SortableItem from '@/components/ui/drag-and-drop'

const SETTING_KEY = 'freebies'

type ExtendedPageBanner = PageBanner & {
  id: UniqueIdentifier
}

type MediaFiles = {
  desktop?: FileWithPath & { preview: string }
  mobile?: FileWithPath & { preview: string }
}

const UpdateFreebiesForm: FC<{
  initialValues?: Settings
  isDialogOpen: boolean
  setIsDialogOpen: (value: boolean) => void
}> = ({ initialValues, isDialogOpen, setIsDialogOpen }) => {
  const form = useForm<CreateFreebies>({
    shouldUseNativeValidation: false,
    defaultValues: {
      value: {
        description: (initialValues?.value as Freebies).description,
        gallery: (initialValues?.value as Freebies).gallery,
        categories: (initialValues?.value as Freebies).categories ?? [{ title: '', image_url: '' }]
      }
    }
  })

  // State for sortable banners and media files
  const [sortableBanner, setSortableBanner] = useState<ExtendedPageBanner[]>([])
  const [mediaFiles, setMediaFiles] = useState<Record<string, MediaFiles>>({})

  const { fields } = useFieldArray({
    name: 'value.categories',
    control: form.control
  })

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates
    })
  )

  useEffect(() => {
    if (isDialogOpen) {
      form.reset({
        value: {
          description: (initialValues?.value as Freebies).description,
          gallery: (initialValues?.value as Freebies).gallery,
          categories: (initialValues?.value as Freebies).categories ?? [
            { title: '', image_url: '' }
          ]
        }
      })

      // Initialize sortable banners from gallery
      if ((initialValues?.value as Freebies).gallery) {
        const galleryBanners = (initialValues?.value as Freebies).gallery.map((item, index) => ({
          id: `banner-${index}` as UniqueIdentifier,
          desktop: {
            url: item.url,
            name: item.name,
            type: item.type,
            metadata: item.metadata
          },
          mobile: {
            url: item.url,
            name: item.name,
            type: item.type,
            metadata: item.metadata
          }
        }))
        setSortableBanner(galleryBanners)
      }
    }
  }, [isDialogOpen, initialValues])

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event

    if (active.id !== over?.id) {
      setSortableBanner((items) => {
        if (!items || !over) {
          return items
        }

        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)

        // Update form values to match new order
        const currentGallery = form.getValues('value.gallery') ?? []
        const [galleryItem] = currentGallery.splice(oldIndex, 1)
        currentGallery.splice(newIndex, 0, galleryItem)
        form.setValue('value.gallery', currentGallery)

        return arrayMove(items, oldIndex, newIndex)
      })
    }
  }

  const addNewBanner = () => {
    const newId = `banner-${Date.now()}`
    const newBanner: ExtendedPageBanner = {
      id: newId,
      desktop: { name: '', type: 'image' },
      mobile: { name: '', type: 'image' }
    }

    setSortableBanner((prev) => [...prev, newBanner])

    // Add to gallery as well (for backward compatibility)
    const currentGallery = form.getValues('value.gallery') ?? []
    form.setValue('value.gallery', [
      ...currentGallery,
      { name: '', type: 'image' as 'video' | 'image' }
    ])
  }

  const handleMediaUpload = (
    bannerId: string,
    device: 'desktop' | 'mobile',
    files: FileWithPath[]
  ) => {
    if (files.length === 0) return

    const file = files[0]
    const preview = Object.assign(file, {
      preview: URL.createObjectURL(file)
    })

    // Update media files state
    setMediaFiles((prev) => ({
      ...prev,
      [bannerId]: {
        ...prev[bannerId],
        [device]: preview
      }
    }))

    // Update banner in sortable state
    setSortableBanner((prev) =>
      prev.map((banner) => {
        if (banner.id === bannerId) {
          return {
            ...banner,
            [device]: {
              ...banner[device],
              name: file.name,
              type: file.type.split('/')[0] as 'video' | 'image',
              media_file: preview
            }
          }
        }
        return banner
      })
    )

    // Update gallery for backward compatibility
    const bannerIndex = sortableBanner.findIndex((banner) => banner.id === bannerId)
    if (bannerIndex !== -1) {
      const currentGallery = form.getValues('value.gallery') ?? []
      if (currentGallery[bannerIndex]) {
        currentGallery[bannerIndex] = {
          ...currentGallery[bannerIndex],
          name: file.name,
          type: file.type.split('/')[0] as 'video' | 'image',
          media_file: preview
        }
        form.setValue('value.gallery', currentGallery)
      }
    }
  }

  const removeBanner = (bannerId: string) => {
    const bannerIndex = sortableBanner.findIndex((banner) => banner.id === bannerId)

    setSortableBanner((prev) => prev.filter((banner) => banner.id !== bannerId))

    // Remove from gallery as well
    const currentGallery = form.getValues('value.gallery') ?? []
    currentGallery.splice(bannerIndex, 1)
    form.setValue('value.gallery', currentGallery)

    // Clean up media files
    setMediaFiles((prev) => {
      const newMediaFiles = { ...prev }
      delete newMediaFiles[bannerId]
      return newMediaFiles
    })
  }

  const removeMedia = (bannerId: string, device: 'desktop' | 'mobile') => {
    // Update media files state
    setMediaFiles((prev) => ({
      ...prev,
      [bannerId]: {
        ...prev[bannerId],
        [device]: undefined
      }
    }))

    // Update banner in sortable state
    setSortableBanner((prev) =>
      prev.map((banner) => {
        if (banner.id === bannerId) {
          return {
            ...banner,
            [device]: {
              ...banner[device],
              name: '',
              url: undefined,
              media_file: undefined
            }
          }
        }
        return banner
      })
    )

    // Update gallery for backward compatibility
    const bannerIndex = sortableBanner.findIndex((banner) => banner.id === bannerId)
    if (bannerIndex !== -1) {
      const currentGallery = form.getValues('value.gallery') ?? []
      if (currentGallery[bannerIndex]) {
        currentGallery[bannerIndex] = {
          ...currentGallery[bannerIndex],
          name: '',
          url: undefined,
          media_file: undefined
        }
        form.setValue('value.gallery', currentGallery)
      }
    }
  }

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      // Collect all files that need to be uploaded from the new banner structure
      const filesToUpload: File[] = []
      const uploadMapping: Array<{
        bannerIndex: number
        device: 'desktop' | 'mobile'
        fileIndex: number
      }> = []

      // Process sortable banners for file uploads
      sortableBanner.forEach((banner, bannerIndex) => {
        ;['desktop', 'mobile'].forEach((device) => {
          const deviceData = banner[device as 'desktop' | 'mobile']
          if (deviceData?.media_file && !deviceData.url) {
            uploadMapping.push({
              bannerIndex,
              device: device as 'desktop' | 'mobile',
              fileIndex: filesToUpload.length
            })
            filesToUpload.push(deviceData.media_file as File)
          }
        })
      })

      // Upload files if any
      if (filesToUpload.length > 0) {
        const { data: mediaResponse } = await FileService.upload(filesToUpload)

        // Update gallery with uploaded URLs (maintaining backward compatibility)
        uploadMapping.forEach(({ bannerIndex, fileIndex }) => {
          if (values.value.gallery[bannerIndex]) {
            values.value.gallery[bannerIndex].url = mediaResponse.uploads[fileIndex].url
            // Remove media_file before sending to server
            delete values.value.gallery[bannerIndex].media_file
          }
        })
      }

      // Also handle legacy gallery uploads for backward compatibility
      const galleryMedia: File[] = []
      const galleryMediaIndices = []

      for (const index in values.value.gallery) {
        const item = values.value.gallery[index]
        if (!item.media_file) continue
        galleryMedia.push(item.media_file)
        galleryMediaIndices.push(index)
      }

      if (galleryMedia.length) {
        const { data } = await FileService.upload(galleryMedia)
        for (const fileIndex in galleryMediaIndices) {
          const valueIndex = Number(galleryMediaIndices[fileIndex])
          values.value.gallery[valueIndex].url = data.uploads[fileIndex].url
          delete values.value.gallery[valueIndex].media_file
        }
      }

      const categoryMedia: File[] = []
      const categoryMediaIndices = []

      for (const index in values.value.categories) {
        const category = values.value.categories[index]
        if (!category.image) continue
        categoryMedia.push(category.image)
        categoryMediaIndices.push(index)
      }

      if (categoryMedia.length) {
        const { data } = await FileService.upload(categoryMedia)
        for (const fileIndex in categoryMediaIndices) {
          const valueIndex = Number(categoryMediaIndices[fileIndex])
          values.value.categories[valueIndex].image_url = data.uploads[fileIndex].url
          delete values.value.categories[valueIndex].image
        }
      }

      console.log('submit values', values)

      const { data } = await SettingService.updateSetting(SETTING_KEY, values)
      console.log('response', data)

      if (data.success) {
        setIsDialogOpen(false)
        mutate(
          (key) => typeof key === 'string' && key.includes(SettingService.getSetting(SETTING_KEY))
        )
        toast({
          title: 'Freebies updated',
          variant: 'success'
        })
      } else {
        toast({
          title: 'Action failed, please try again',
          variant: 'destructive'
        })
      }
    } catch (e) {
      console.log(e)
      toast({
        title: 'Action failed, please try again',
        variant: 'destructive'
      })
    }
  })

  return (
    <DialogContent className="max-h-[80vh] max-w-5xl overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="flex items-center justify-between">
          Edit Freebies Page
          <div className="flex space-x-2">
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              type="submit"
              form="update-freebies-form"
              disabled={form.formState.isSubmitting}
              onClick={() => {
                form.clearErrors()
              }}
            >
              {form.formState.isSubmitting && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              Save
            </Button>
          </div>
        </DialogTitle>
      </DialogHeader>

      <Form {...form}>
        <form id="update-freebies-form" className="grid gap-2" onSubmit={onSubmit}>
          <FormField
            control={form.control}
            name={`value.description`}
            rules={{ required: 'This is required' }}
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Page Description</FormLabel>
                <FormControl>
                  <EditorComponent content={field.value} onChange={field.onChange} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Primary Banners Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <FormLabel className="text-lg">Primary Banners</FormLabel>
              <Button type="button" variant="outline" size="sm" onClick={addNewBanner}>
                Add Banner
              </Button>
            </div>

            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext items={sortableBanner} strategy={verticalListSortingStrategy}>
                <div className="space-y-6">
                  {sortableBanner.map((banner, index) => (
                    <SortableItem key={banner.id} id={banner.id}>
                      <div className="border rounded-lg p-4 space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <GripHorizontalIcon className="h-4 w-4 text-gray-400" />
                            <span className="text-sm font-medium">Banner {index + 1}</span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={(event) => {
                              event.stopPropagation()
                              event.preventDefault()
                              console.log('test', banner.id)
                              removeBanner(banner.id as string)
                            }}
                            onMouseDown={(event) => {
                              // Prevent drag from starting when clicking the button
                              event.stopPropagation()
                            }}
                            onPointerDown={(event) => {
                              // Additional prevention for pointer events
                              event.stopPropagation()
                            }}
                          >
                            <Trash2Icon className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* Desktop Upload */}
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Monitor className="h-4 w-4" />
                              <Label className="text-sm font-medium">Desktop</Label>
                            </div>

                            {!banner.desktop?.url && !mediaFiles[banner.id as string]?.desktop ? (
                              <Dropzone
                                accept={{
                                  'image/*': ['.jpeg', '.jpg', '.png'],
                                  'video/*': ['.mp4']
                                }}
                                description="Drop desktop asset here"
                                multiple={false}
                                onDrop={(files) =>
                                  handleMediaUpload(banner.id as string, 'desktop', files)
                                }
                              />
                            ) : (
                              <div className="space-y-2">
                                {banner.desktop?.type === 'image' && (
                                  <Image
                                    src={
                                      mediaFiles[banner.id as string]?.desktop?.preview ||
                                      banner.desktop?.url ||
                                      ''
                                    }
                                    layout="constrained"
                                    width={200}
                                    height={120}
                                    className="rounded-md border"
                                  />
                                )}
                                {banner.desktop?.type === 'video' && (
                                  <video
                                    src={
                                      mediaFiles[banner.id as string]?.desktop?.preview ||
                                      banner.desktop?.url ||
                                      ''
                                    }
                                    className="rounded-md border w-full max-w-[200px] h-[120px] object-cover"
                                  />
                                )}
                                <div className="flex items-center justify-between">
                                  <span className="text-xs text-gray-500">
                                    {banner.desktop?.name}
                                  </span>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={(event) => {
                                      event.stopPropagation()
                                      event.preventDefault()
                                      console.log('test', banner.id)
                                      removeMedia(banner.id as string, 'desktop')
                                    }}
                                    onMouseDown={(event) => {
                                      // Prevent drag from starting when clicking the button
                                      event.stopPropagation()
                                    }}
                                    onPointerDown={(event) => {
                                      // Additional prevention for pointer events
                                      event.stopPropagation()
                                    }}
                                  >
                                    <Trash2Icon className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Mobile Upload */}
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Smartphone className="h-4 w-4" />
                              <Label className="text-sm font-medium">Mobile</Label>
                            </div>

                            {!banner.mobile?.url && !mediaFiles[banner.id as string]?.mobile ? (
                              <Dropzone
                                accept={{
                                  'image/*': ['.jpeg', '.jpg', '.png'],
                                  'video/*': ['.mp4']
                                }}
                                description="Drop mobile asset here"
                                multiple={false}
                                onDrop={(files) =>
                                  handleMediaUpload(banner.id as string, 'mobile', files)
                                }
                              />
                            ) : (
                              <div className="space-y-2">
                                {banner.mobile?.type === 'image' && (
                                  <Image
                                    src={
                                      mediaFiles[banner.id as string]?.mobile?.preview ||
                                      banner.mobile?.url ||
                                      ''
                                    }
                                    layout="constrained"
                                    width={120}
                                    height={200}
                                    className="rounded-md border"
                                  />
                                )}
                                {banner.mobile?.type === 'video' && (
                                  <video
                                    src={
                                      mediaFiles[banner.id as string]?.mobile?.preview ||
                                      banner.mobile?.url ||
                                      ''
                                    }
                                    className="rounded-md border w-full max-w-[120px] h-[200px] object-cover"
                                  />
                                )}
                                <div className="flex items-center justify-between">
                                  <span className="text-xs text-gray-500">
                                    {banner.mobile?.name}
                                  </span>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={(event) => {
                                      event.stopPropagation()
                                      event.preventDefault()
                                      console.log('test', banner.id)
                                      removeMedia(banner.id as string, 'mobile')
                                    }}
                                    onMouseDown={(event) => {
                                      // Prevent drag from starting when clicking the button
                                      event.stopPropagation()
                                    }}
                                    onPointerDown={(event) => {
                                      // Additional prevention for pointer events
                                      event.stopPropagation()
                                    }}
                                  >
                                    <Trash2Icon className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </SortableItem>
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>

          <FormItem className="flex flex-col pt-4 mt-4 border-t-2">
            <FormLabel className="text-lg">Categories</FormLabel>
          </FormItem>

          {fields.map((item, i) => {
            const imageFile = form.watch(`value.categories.${i}.image`)
            const imageFileWithPath = imageFile
              ? (imageFile as unknown as FileWithPath & { preview: string })
              : undefined

            return (
              <div key={item.id} className={cx('grid gap-2', i > 0 && 'border-t-2 mt-4')}>
                {i > 0 && <FormItem className="flex flex-col"></FormItem>}
                <FormLabel className="flex gap-2">
                  {/* {fields.length > 1 && (
                    <Trash2Icon
                      size={16}
                      color="red"
                      onClick={() => {
                        remove(i)
                      }}
                    />
                  )} */}
                </FormLabel>
                <FormField
                  control={form.control}
                  name={`value.categories.${i}.title`}
                  rules={{ required: 'This is required' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Title" disabled />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`value.categories.${i}.image`}
                  // rules={{ required: 'This is required' }}
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Image</FormLabel>
                      <FormControl>
                        <Dropzone
                          multiple={false}
                          onDrop={(acceptedFiles) => {
                            if (imageFileWithPath?.path != acceptedFiles[0].path) {
                              const preview = Object.assign(acceptedFiles[0], {
                                preview: URL.createObjectURL(acceptedFiles[0])
                              })

                              form.setValue(`value.categories.${i}.image`, preview as File, {
                                shouldValidate: true
                              })
                            }
                          }}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                      {imageFileWithPath && (
                        <div className="mt-2 flex flex-col space-y-4">
                          <div className="grid grid-cols-[150px_1fr_36px] items-center space-x-2">
                            {imageFileWithPath.type.startsWith('image') && (
                              <Image
                                key={imageFileWithPath.path}
                                src={imageFileWithPath.preview ?? ''}
                                height={150}
                                width={150}
                                objectFit="contain"
                                className="rounded-md"
                              />
                            )}
                            {imageFileWithPath.type.startsWith('video') && (
                              <video
                                // controls
                                key={imageFileWithPath.path}
                                src={imageFileWithPath.preview ?? ''}
                                height={150}
                                width={150}
                                className="rounded-md"
                              />
                            )}
                            <div className="flex flex-col">
                              <Label className="text-xs font-normal">
                                {imageFileWithPath.path}
                              </Label>
                              <Label className="text-xs font-normal text-gray-500">
                                {bytesToSize(imageFileWithPath.size)}
                              </Label>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation()

                                    form.setValue(`value.categories.${i}.image`, undefined)
                                  }}
                                  className="space-x-2"
                                >
                                  <Trash2Icon size="16" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      )}
                      {!imageFileWithPath && item.image_url && (
                        <div className="mt-2 flex flex-col space-y-4">
                          <div className="grid grid-cols-[150px_1fr_36px] items-center space-x-2">
                            <Image
                              key={item.id}
                              src={item.image_url ?? ''}
                              height={150}
                              width={150}
                              objectFit="contain"
                              className="rounded-md"
                            />
                            <Label className="text-xs font-normal">Uploaded</Label>
                          </div>
                        </div>
                      )}
                    </FormItem>
                  )}
                />
              </div>
            )
          })}
        </form>
      </Form>
    </DialogContent>
  )
}

export default UpdateFreebiesForm
